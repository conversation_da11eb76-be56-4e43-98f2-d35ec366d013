import { Component, On<PERSON><PERSON>roy, OnInit, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ModalComponent } from '../../components/atoms/modal/modal.component';
import { ButtonComponent } from '../../components/atoms/button/button.component';
import { SearchableDropdownComponent, DropdownOption } from '../../components/molecules/searchable-dropdown/searchable-dropdown.component';
import { IconComponent } from '../../components/atoms/icon/icon.component';
import { ModalService } from '../../services/modal.service';
import { ToastService } from '../../services/toast.service';
import { DepartmentsStore, EmployeesStore } from '../../../core/state';
import { Department, DepartmentInput } from '../../../core/models/department.interface';
import { Employee } from '../../../core/state/employees/employees.state';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-edit-department-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ModalComponent,
    ButtonComponent,
    IconComponent,
    SearchableDropdownComponent
  ],
  templateUrl: './edit-department-modal.component.html',
  styleUrl: './edit-department-modal.component.scss'
})
export class EditDepartmentModalComponent implements OnInit, OnDestroy {
  private fb = inject(FormBuilder);
  private modalService = inject(ModalService);
  private toastService = inject(ToastService);
  private departmentsStore = inject(DepartmentsStore);
  private employeesStore = inject(EmployeesStore);

  departmentForm!: FormGroup;
  isSubmitting = false;
  department: Department | null = null;
  isLoadingEmployees = false;
  employeeLoadError: string | null = null;

  // Dropdown options
  statusOptions: DropdownOption[] = [
    {
      value: 'active',
      label: 'Active',
      icon: 'fa fa-check-circle',
      subtitle: 'Department is operational'
    },
    {
      value: 'inactive',
      label: 'Inactive',
      icon: 'fa fa-times-circle',
      subtitle: 'Department is not operational'
    }
  ];

  managerOptions: DropdownOption[] = [];
  private managerOptionsLoaded = false;
  private destroy$ = new Subject<void>();
  private managerPatched = false;

  constructor() {
    // Set up effects to watch for employee data changes
    effect(() => {
      const employees = this.employeesStore.employees();
      const isLoadingEmployees = this.employeesStore.isLoading();

      this.isLoadingEmployees = isLoadingEmployees;

      if (employees && employees.length > 0) {
        this.managerOptions = [
          { value: null, label: 'No Manager', subtitle: 'No manager assigned' },
          ...employees
            .filter(emp => emp.full_name)
            .map(emp => ({
              value: emp.id,
              label: `${emp.full_name}`.trim(),
              subtitle: emp.position || 'Employee'
            }))
        ];
        this.managerOptionsLoaded = true;
      } else if (!isLoadingEmployees) {
        this.managerOptions = [
          { value: null, label: 'No Manager', subtitle: 'No manager assigned' }
        ];
        this.managerOptionsLoaded = true;
      }

      // Ensure manager field is set after employees are loaded and department is present
      if (this.department && this.departmentForm && this.managerOptionsLoaded && !this.managerPatched) {
        this.departmentForm.patchValue({ manager: this.department.manager });
        this.managerPatched = true;
      }
    });
  }

  ngOnInit(): void {
    this.initForm();
    this.loadEmployees();

    // Subscribe to modal data to get the department to edit
    this.modalService.getData<Department>('edit-department-modal')
      .pipe(takeUntil(this.destroy$))
      .subscribe(department => {
        if (department) {
          this.department = department;
          this.patchForm(department);
          this.managerPatched = false; // Reset so effect can patch when options are ready
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Initialize the form with validation
   */
  private initForm(): void {
    this.departmentForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', Validators.maxLength(500)],
      manager: [null],
      status: ['active', [Validators.required]]
    });
  }

  /**
   * Patch form with department data
   */
  private patchForm(department: Department): void {
    this.departmentForm.patchValue({
      name: department.name,
      description: department.description,
      manager: department.manager,
      status: department.status || 'active'
    });
  }

  /**
   * Load employees for manager dropdown
   */
  private loadEmployees(): void {
    this.employeesStore.loadEmployees({});
  }

  /**
   * Check if a field is invalid and has been touched
   * @param field The form field name
   * @returns True if the field is invalid and touched
   */
  isFieldInvalid(field: string): boolean {
    const formControl = this.departmentForm.get(field);
    return !!formControl && formControl.invalid && (formControl.dirty || formControl.touched);
  }

  /**
   * Submit the form
   */
  onSubmit(): void {
    if (this.departmentForm.invalid || !this.department) {
      // Mark all fields as touched to show validation errors
      Object.keys(this.departmentForm.controls).forEach(key => {
        this.departmentForm.get(key)?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    const departmentData: DepartmentInput = this.departmentForm.value;

    // Call the store method to update the department
    this.departmentsStore.updateDepartment({
      id: this.department.id,
      data: departmentData
    });

    // Set up a watcher for the store state
    const errorWatcher = setInterval(() => {
      const currentError = this.departmentsStore.error();
      if (currentError) {
        clearInterval(errorWatcher);
        this.toastService.error('Failed to update department: ' + currentError);
        this.isSubmitting = false;
      }

      // Check if departments were updated (success case)
      if (!this.departmentsStore.isLoading()) {
        clearInterval(errorWatcher);
        this.toastService.success('Department updated successfully');
        this.closeModal();
        this.isSubmitting = false;
      }
    }, 100);
  }

  /**
   * Close the modal
   */
  closeModal(): void {
    this.modalService.close('edit-department-modal');
  }
}
