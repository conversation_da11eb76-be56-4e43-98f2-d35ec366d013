@import '../../../../styles/variables/_colors';

:host {
  display: block;
  padding: 24px;
  background-color: var(--background-color, #f5f5f5);
  min-height: 100vh;
}

// Loading and Error States
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;

  .spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color, #e0e0e0);
    border-top: 4px solid var(--primary-color, #ff6b35);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .error-icon {
    color: var(--error-color, #f44336);
    margin-bottom: 16px;
  }

  h3 {
    margin: 16px 0 8px 0;
    color: var(--text-primary, #333);
  }

  p {
    color: var(--text-secondary, #666);
    margin-bottom: 24px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Department Detail Container
.department-detail {
  max-width: 1200px;
  margin: 0 auto;
}

// Breadcrumb Navigation
.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  font-size: 14px;

  .breadcrumb-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--primary-color, #ff6b35);
    transition: color 0.2s ease;

    &:hover {
      color: var(--primary-dark, #e55a2b);
    }

    app-icon {
      margin-right: 4px;
    }
  }

  .breadcrumb-separator {
    margin: 0 12px;
    color: var(--text-secondary, #666);
  }

  .breadcrumb-current {
    color: var(--text-primary, #333);
    font-weight: 500;
  }
}

// Header Section
.header-section {
  margin-bottom: 32px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
    }
  }

  .title-section {
    flex: 1;

    .department-title {
      font-size: 32px;
      font-weight: 600;
      margin: 0 0 12px 0;
      color: var(--text-primary, #333);
    }

    .status-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;

    @media (max-width: 768px) {
      justify-content: flex-end;
    }

    app-button {
      min-width: 120px;
    }
  }
}

// Statistics Section
.statistics-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;

  .stat-card {
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .stat-icon {
        font-size: 32px;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        background-color: var(--primary-light, #fff3f0);
        color: var(--primary-color, #ff6b35);

        &.active {
          background-color: #e8f5e8;
          color: #4caf50;
        }

        &.inactive {
          background-color: #ffebee;
          color: #f44336;
        }
      }

      .stat-info {
        h3 {
          font-size: 24px;
          font-weight: 600;
          margin: 0;
          color: var(--text-primary, #333);
        }

        p {
          font-size: 14px;
          margin: 4px 0 0 0;
          color: var(--text-secondary, #666);
        }
      }
    }
  }
}

// Overview Card
.overview-card {
  margin-bottom: 32px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: var(--surface-color, #fff);
  padding: 24px;

  .card-header {
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color, #e0e0e0);
    margin-bottom: 24px;

    .card-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--text-primary, #333);
      margin: 0;
    }
  }

  .card-content {
    padding: 0;
  }

  .overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;

    .overview-item {
      label {
        display: block;
        font-weight: 600;
        color: var(--text-primary, #333);
        margin-bottom: 8px;
        font-size: 14px;
      }

      p {
        margin: 0;
        color: var(--text-secondary, #666);
        line-height: 1.5;
      }

      .manager-info {
        .manager-details {
          display: flex;
          align-items: center;
          gap: 12px;

          .manager-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            background-color: var(--background-light, #f0f0f0);

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .manager-name {
            font-weight: 500;
            color: var(--text-primary, #333);
          }
        }

        .no-manager {
          color: var(--text-secondary, #666);
          font-style: italic;
        }
      }
    }
  }
}

// Employees Card
.employees-card {
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: var(--surface-color, #fff);
  padding: 24px;

  .card-header {
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color, #e0e0e0);
    margin-bottom: 24px;

    .card-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--text-primary, #333);
      margin: 0;
    }
  }

  .card-content {
    padding: 0;
  }

  .employees-grid {
    display: grid;
    gap: 16px;

    .employee-card {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      border: 1px solid var(--border-color, #e0e0e0);
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      background-color: var(--surface-color, #fff);

      &:hover {
        border-color: var(--primary-color, #ff6b35);
        box-shadow: 0 2px 8px rgba(255, 107, 53, 0.15);
        transform: translateY(-1px);
      }

      .employee-avatar {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        overflow: hidden;
        background-color: var(--background-light, #f0f0f0);
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .employee-info {
        flex: 1;
        min-width: 0;

        .employee-name {
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 4px 0;
          color: var(--text-primary, #333);
        }

        .employee-email {
          font-size: 14px;
          margin: 0 0 4px 0;
          color: var(--text-secondary, #666);
        }

        .employee-position {
          font-size: 14px;
          margin: 0 0 8px 0;
          color: var(--text-secondary, #666);
        }

        .status-badge {
          display: inline-block;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          text-transform: uppercase;
        }
      }

      .view-icon {
        color: var(--text-secondary, #666);
        transition: color 0.2s ease;
      }

      &:hover .view-icon {
        color: var(--primary-color, #ff6b35);
      }
    }
  }

  .no-employees {
    text-align: center;
    padding: 48px 24px;

    .no-data-icon {
      font-size: 64px;
      color: var(--text-disabled, #ccc);
      margin-bottom: 16px;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: var(--text-primary, #333);
    }

    p {
      color: var(--text-secondary, #666);
      margin: 0;
    }
  }
}

// Status Chips
.status-active {
  background-color: #e8f5e8 !important;
  color: #4caf50 !important;
}

.status-inactive {
  background-color: #ffebee !important;
  color: #f44336 !important;
}

// Responsive Design
@media (max-width: 768px) {
  :host {
    padding: 16px;
  }

  .statistics-section {
    grid-template-columns: 1fr;
  }

  .overview-grid {
    grid-template-columns: 1fr !important;
  }

  .header-section .header-content {
    .title-section .department-title {
      font-size: 24px;
    }

    .action-buttons {
      app-button {
        min-width: auto;
        flex: 1;
      }
    }
  }

  .employees-grid .employee-card {
    flex-direction: column;
    text-align: center;

    .employee-info {
      text-align: center;
    }

    .view-icon {
      display: none;
    }
  }
}
