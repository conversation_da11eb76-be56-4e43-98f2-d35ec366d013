<div class="department-table">
  <div class="department-table__header">
    <div class="department-table__title">
      <h2>Departments</h2>
      <span class="department-table__count">{{ totalCount() }} total</span>
    </div>

    <div class="department-table__actions">
      <app-button
        label="Add Department"
        variant="primary"
        icon="fa fa-plus"
        (onClick)="openAddDepartmentModal()">
      </app-button>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="department-table__filters" [formGroup]="filterForm">
    <div class="department-table__filters-row">
      <!-- Search Filter -->
      <div class="department-table__filter-group">
        <label class="department-table__filter-label">Search</label>
        <app-search-input
          formControlName="search"
          placeholder="Search departments..."
          (search)="onSearchChange($event)"
          (clear)="onSearchChange('')">
        </app-search-input>
      </div>

      <!-- Status Filter -->
      <div class="department-table__filter-group">
        <label class="department-table__filter-label">Status</label>
        <app-multi-select-dropdown
          [options]="statusOptions"
          [showSearch]="false"
          placeholder="Select status..."
          formControlName="status">
        </app-multi-select-dropdown>
      </div>

      <!-- Manager Filter -->
      <div class="department-table__filter-group">
        <label class="department-table__filter-label">Manager</label>
        <app-multi-select-dropdown
          [options]="managerOptions"
          [showSearch]="true"
          [disabled]="isLoadingEmployees"
          searchPlaceholder="Search managers..."
          [placeholder]="isLoadingEmployees ? 'Loading managers...' : 'Select managers...'"
          formControlName="manager_name">
        </app-multi-select-dropdown>
      </div>

      <!-- Ordering Filter -->
      <div class="department-table__filter-group">
        <label class="department-table__filter-label">Sort By</label>
        <select
          class="department-table__filter-select"
          formControlName="ordering">
          <option *ngFor="let option of orderingOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <!-- Clear Filters Button -->
      <div class="department-table__filter-group department-table__filter-group--actions">
        <app-button
          label="Clear All Filters"
          variant="secondary"
          size="sm"
          icon="fa fa-times"
          (onClick)="clearAllFilters()">
        </app-button>
      </div>
    </div>
  </div>

  <div class="department-table__table-container">
    <div class="department-table__content">
      <table class="department-table__table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Description</th>
          <th>Manager</th>
          <th>Employees</th>
          <th>Status</th>
          <th>Created</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngIf="!isLoading() && departments().length > 0">
          <tr *ngFor="let department of departments()">
            <td>{{ department.name }}</td>
            <td>{{ department.description || 'N/A' }}</td>
            <td>{{ department.manager_name || 'Not assigned' }}</td>
            <td>{{ department.employee_count || 0 }}</td>
            <td>
              <span class="department-table__status-badge"
                    [ngClass]="'department-table__status-badge--' + (department.status || 'active')">
                <app-icon [name]="department.status === 'active' ? 'fa fa-check-circle' : 'fa fa-times-circle'" size="sm"></app-icon>
                {{ (department.status || 'active') | titlecase }}
              </span>
            </td>
            <td>{{ department.created_at | date:'mediumDate' }}</td>
            <td class="department-table__actions-cell">
              <button class="department-table__action-btn" (click)="viewDepartment(department)" title="View">
                <app-icon name="fa fa-eye" size="sm"></app-icon>
              </button>
              <button class="department-table__action-btn" (click)="openEditDepartmentModal(department)" title="Edit">
                <app-icon name="fa fa-edit" size="sm"></app-icon>
              </button>
              <button class="department-table__action-btn department-table__action-btn--danger" (click)="deleteDepartment(department)" title="Delete">
                <app-icon name="fa fa-trash" size="sm"></app-icon>
              </button>
            </td>
          </tr>
        </ng-container>

        <tr *ngIf="!isLoading() && departments().length === 0">
          <td colspan="7" class="department-table__empty">
            <div class="department-table__empty-content">
              <app-icon name="fa fa-folder-open" size="lg"></app-icon>
              <p>No departments found</p>
              <p *ngIf="searchTerm()">Try adjusting your search criteria</p>
              <app-button
                *ngIf="!searchTerm()"
                label="Add Department"
                variant="primary"
                size="sm"
                (onClick)="openAddDepartmentModal()">
              </app-button>
            </div>
          </td>
        </tr>

        <tr *ngIf="isLoading()">
          <td colspan="7" class="department-table__loading">
            <div class="department-table__loading-spinner">
              <app-icon name="fa fa-spinner fa-spin" size="lg"></app-icon>
              <p>Loading departments...</p>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    </div>

    <!-- Pagination inside table container -->
    <div class="department-table__pagination" *ngIf="!isLoading() && departments().length > 0">
      <app-pagination
        [currentPage]="page()"
        [pageSize]="pageSize()"
        [totalItems]="totalCount()"
        [pageSizeOptions]="pageSizeOptions"
        (pageChange)="onPageChange($event)"
        (pageSizeChange)="onPageSizeChange($event)">
      </app-pagination>
    </div>
  </div>
</div>
