import { Component, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';
import { DepartmentsStore, EmployeesStore } from '../../../../core/state';
import { Department } from '../../../../core/models/department.interface';
import { ButtonComponent } from '../../atoms/button/button.component';
import { SearchInputComponent } from '../../atoms/search-input/search-input.component';
import { PaginationComponent } from '../../atoms/pagination/pagination.component';
import { IconComponent } from '../../atoms/icon/icon.component';
import { MultiSelectDropdownComponent } from '../../molecules/multi-select-dropdown/multi-select-dropdown.component';
import { DropdownOption } from '../../atoms/dropdown-select/dropdown-select.component';
import { ModalService } from '../../../services/modal.service';
import { ToastService } from '../../../services/toast.service';
import { ModalService as CoreModalService } from '../../../../core/services/modal.service';

@Component({
  selector: 'app-department-table',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonComponent,
    SearchInputComponent,
    PaginationComponent,
    IconComponent,
    MultiSelectDropdownComponent
  ],
  templateUrl: './department-table.component.html',
  styleUrl: './department-table.component.scss'
})
export class DepartmentTableComponent implements OnInit, OnDestroy {
  private departmentsStore = inject(DepartmentsStore);
  private employeesStore = inject(EmployeesStore);
  private router = inject(Router);
  private modalService = inject(ModalService);
  private coreModalService = inject(CoreModalService);
  private toastService = inject(ToastService);
  private fb = inject(FormBuilder);

  // Cleanup subject
  private destroy$ = new Subject<void>();

  // Access state from the store using signals
  departments = this.departmentsStore.departments;
  isLoading = this.departmentsStore.isLoading;
  error = this.departmentsStore.error;
  page = this.departmentsStore.page;
  pageSize = this.departmentsStore.pageSize;
  totalCount = this.departmentsStore.totalCount;
  totalPages = this.departmentsStore.totalPages;
  searchTerm = this.departmentsStore.searchTerm;
  filters = this.departmentsStore.filters;

  // Page size options
  pageSizeOptions = [5, 10, 25, 50];

  // Filter form
  filterForm!: FormGroup;

  // Filter options
  statusOptions: DropdownOption[] = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' }
  ];

  managerOptions: DropdownOption[] = [];

  orderingOptions: DropdownOption[] = [
    { value: '', label: 'Default' },
    { value: 'name', label: 'Name (A-Z)' },
    { value: '-name', label: 'Name (Z-A)' },
    { value: 'created_at', label: 'Created Date (Oldest)' },
    { value: '-created_at', label: 'Created Date (Newest)' },
    { value: 'employee_count', label: 'Employee Count (Low to High)' },
    { value: '-employee_count', label: 'Employee Count (High to Low)' }
  ];

  // Loading states for individual components
  isLoadingEmployees = false;

  constructor() {
    // Initialize filter form with proper initial values from store
    this.filterForm = this.fb.group({
      status: [[]],
      manager: [[]],
      search: [''],
      ordering: ['']
    });

    // Load employees for manager dropdown using effect
    effect(() => {
      const employees = this.employeesStore.employees();
      const isLoadingEmployees = this.employeesStore.isLoading();

      this.isLoadingEmployees = isLoadingEmployees;

      if (employees && employees.length > 0) {
        this.managerOptions = employees
          .filter(emp => emp.full_name && emp.id) // Filter out employees with undefined names or IDs
          .map(emp => ({
            value: emp.id, // Use employee ID as number value
            label: `${emp.full_name}`.trim() // Display full name as label
          }));

      } else if (!isLoadingEmployees) {
        // Clear options if no employees are loaded and not loading
        this.managerOptions = [];
      }
    });

    // Sync form with store state changes
    effect(() => {
      const currentFilters = this.filters();

      // Only patch if values are different to avoid infinite loops
      const formValues = this.filterForm.value;
      const needsUpdate =
        JSON.stringify(formValues.status || []) !== JSON.stringify(currentFilters.status || []) ||
        JSON.stringify(formValues.manager || []) !== JSON.stringify(currentFilters.manager || []) ||
        (formValues.search || '') !== (currentFilters.search || '') ||
        (formValues.ordering || '') !== (currentFilters.ordering || '');

      if (needsUpdate) {
        this.filterForm.patchValue({
          status: currentFilters.status || [],
          manager: currentFilters.manager || [],
          search: currentFilters.search || '',
          ordering: currentFilters.ordering || ''
        }, { emitEvent: false });
      }
    });
  }

  ngOnInit(): void {
    // Load departments and employees on component initialization
    this.loadDepartments();
    this.loadEmployees();
    this.initializeFilters();
    this.setupFilterSubscriptions();


  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load departments with current pagination and search settings
   */
  loadDepartments(): void {
    this.departmentsStore.loadDepartments({});
  }

  /**
   * Handle page change
   * @param page The new page number
   */
  onPageChange(page: number): void {
    this.departmentsStore.setPage(page);
  }

  /**
   * Handle page size change
   * @param pageSize The new page size
   */
  onPageSizeChange(pageSize: number): void {
    this.departmentsStore.setPageSize(pageSize);
  }

  /**
   * Handle search
   * @param searchTerm The search term
   */
  onSearch(searchTerm: string): void {
    this.departmentsStore.setSearchTerm(searchTerm);
  }

  /**
   * Clear search
   */
  clearSearch(): void {
    this.departmentsStore.resetSearch();
  }

  /**
   * Open add department modal
   */
  openAddDepartmentModal(): void {
    this.modalService.open({ id: 'add-department-modal' });
  }

  /**
   * Open edit department modal
   * @param department The department to edit
   */
  openEditDepartmentModal(department: Department): void {
    this.modalService.open({
      id: 'edit-department-modal',
      data: department
    });
  }

  /**
   * Delete department
   * @param department The department to delete
   */
  async deleteDepartment(department: Department): Promise<void> {
    const confirmed = await this.coreModalService.confirm({
      title: 'Delete Department',
      message: `Are you sure you want to delete the department "${department.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel'
    });

    if (confirmed) {
      this.departmentsStore.deleteDepartment(department.id);

      // Set up a watcher to monitor loading state
      const loadingWatcher = setInterval(() => {
        if (!this.departmentsStore.isLoading()) {
          clearInterval(loadingWatcher);

          // Check for errors
          const error = this.departmentsStore.error();
          if (error) {
            this.toastService.error(`Failed to delete department: ${error}`);
          } else {
            // Show success toast
            this.toastService.success(`Department "${department.name}" deleted successfully`);
          }
        }
      }, 100);
    }
  }

  /**
   * View department details
   * @param department The department to view
   */
  viewDepartment(department: Department): void {
    this.router.navigate(['/app/departments', department.id]);
  }

  /**
   * Load employees for manager dropdown
   */
  loadEmployees(): void {
    this.employeesStore.loadEmployees({});
  }

  /**
   * Initialize filters from store state
   */
  initializeFilters(): void {
    const currentFilters = this.filters();
    this.filterForm.patchValue({
      status: currentFilters.status,
      manager: currentFilters.manager,
      search: currentFilters.search,
      ordering: currentFilters.ordering
    });
  }

  /**
   * Setup filter form subscriptions with debouncing
   */
  setupFilterSubscriptions(): void {
    // Status filter
    this.filterForm.get('status')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(status => {
        this.departmentsStore.setStatusFilter(status || []);
      });

    // Manager filter
    this.filterForm.get('manager')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(manager => {
        // Handle both string and number values for robustness
        const managerIds = (manager || [])
          .map((id: string | number) => typeof id === 'string' ? parseInt(id, 10) : id)
          .filter((id: number) => !isNaN(id) && id > 0);
        this.departmentsStore.setManagerIdFilter(managerIds);
      });

    // Search filter with debouncing
    this.filterForm.get('search')?.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(search => {
        this.departmentsStore.setSearchFilter(search || '');
      });

    // Ordering filter
    this.filterForm.get('ordering')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(ordering => {
        this.departmentsStore.setOrdering(ordering || '');
      });
  }

  /**
   * Clear all filters
   */
  clearAllFilters(): void {
    this.filterForm.reset({
      status: [],
      manager: [],
      search: '',
      ordering: ''
    });
    this.departmentsStore.clearAllFilters();
  }



  /**
   * Handle ordering change
   */
  onOrderingChange(ordering: string): void {
    this.filterForm.patchValue({ ordering });
  }

  /**
   * Handle search input change
   */
  onSearchChange(searchTerm: string): void {
    this.filterForm.patchValue({ search: searchTerm });
  }

  /**
   * Handle manager selection change
   */
  onManagerSelectionChange(_selectedManagers: (string | number)[]): void {
    // Optional: Add any additional logic for manager selection changes
  }
}
