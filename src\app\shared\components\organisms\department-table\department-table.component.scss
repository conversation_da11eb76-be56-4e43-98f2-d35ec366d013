@import '../../../../../styles/variables/_colors';

.department-table {
  background-color: $white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid $gray-200;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }

  &__title {
    display: flex;
    align-items: baseline;
    gap: 12px;

    h2 {
      font-size: 20px;
      font-weight: 600;
      color: $gray-800;
      margin: 0;
    }
  }

  &__count {
    font-size: 14px;
    color: $gray-600;
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: 16px;

    @media (max-width: 768px) {
      width: 100%;
    }
  }

  &__search {
    width: 280px;

    @media (max-width: 768px) {
      flex: 1;
    }
  }

  // Filters Section
  &__filters {
    padding: 20px 24px;
    border-bottom: 1px solid $gray-200;
    background-color: $gray-100;
  }

  &__filters-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: end;

    @media (max-width: 1024px) {
      gap: 12px;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
    }
  }

  &__filter-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
    min-width: 200px;

    @media (max-width: 768px) {
      min-width: auto;
    }

    &--actions {
      justify-content: flex-end;
      min-width: auto;

      @media (max-width: 768px) {
        justify-content: stretch;
      }
    }
  }

  &__filter-label {
    font-size: 14px;
    font-weight: 500;
    color: $gray-700;
    margin-bottom: 4px;
  }

  &__filter-select {
    padding: 10px 12px;
    border: 1px solid $gray-300;
    border-radius: 8px;
    background-color: $white;
    color: $gray-800;
    font-size: 14px;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 2px rgba($primary, 0.1);
    }

    &:hover {
      border-color: $gray-400;
    }
  }

  &__content {
    overflow-x: auto;
  }

  &__table {
    width: 100%;
    border-collapse: collapse;

    th, td {
      padding: 16px 24px;
      text-align: left;
      border-bottom: 1px solid $gray-200;
    }

    th {
      font-weight: 600;
      color: $gray-700;
      background-color: $gray-100;
      white-space: nowrap;
    }

    td {
      color: $gray-800;
    }

    tbody tr:hover {
      background-color: rgba($primary, 0.03);
    }
  }

  &__actions-cell {
    white-space: nowrap;
    display: flex;
    gap: 8px;
  }

  &__action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    border: none;
    background-color: $gray-100;
    color: $gray-700;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: $gray-200;
    }

    &--danger {
      &:hover {
        background-color: rgba($danger, 0.1);
        color: $danger;
      }
    }
  }

  &__status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
    white-space: nowrap;

    &--active {
      background-color: rgba($success, 0.1);
      color: $success;
      border: 1px solid rgba($success, 0.2);
    }

    &--inactive {
      background-color: rgba($gray-500, 0.1);
      color: $gray-600;
      border: 1px solid rgba($gray-500, 0.2);
    }

    app-icon {
      font-size: 10px;
    }
  }

  &__empty, &__loading {
    height: 300px;
    text-align: center;
  }

  &__empty-content, &__loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 16px;
    color: $gray-600;

    app-icon {
      font-size: 48px;
      color: $gray-400;
    }

    p {
      margin: 0;
    }
  }

  // Table container - matches employee listing style
  &__table-container {
    background-color: $white;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-top: 24px;
  }

  // Pagination section - matches employee listing style
  &__pagination {
    padding: 20px 24px;
    border-top: 1px solid $gray-200;
    background-color: $white;
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-top: 1px solid $gray-200;

    @media (max-width: 576px) {
      flex-direction: column;
      gap: 16px;
    }
  }

  &__page-size {
    display: flex;
    align-items: center;
    gap: 8px;
    color: $gray-700;
    font-size: 14px;

    select {
      padding: 6px 8px;
      border: 1px solid $gray-300;
      border-radius: 4px;
      background-color: $white;
      color: $gray-800;
      font-size: 14px;

      &:focus {
        outline: none;
        border-color: $primary;
        box-shadow: 0 0 0 2px rgba($primary, 0.1);
      }
    }
  }
}
