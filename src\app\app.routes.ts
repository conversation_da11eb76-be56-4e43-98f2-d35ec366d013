import { Routes } from '@angular/router';
import { MainLayoutComponent } from './layout/main-layout/main-layout.component';
import { authGuard, authGuardChild } from './core/guards/auth.guard.fn';
import { maintenanceGuard } from './core/guards/maintenance.guard.fn';

export const routes: Routes = [
  {
    path: '',
    canActivate: [maintenanceGuard],
    loadComponent: () => import('./pages/landing/landing.component').then(m => m.LandingComponent)
  },
  {
    path: 'login',
    canActivate: [maintenanceGuard],
    loadComponent: () => import('./pages/login/login.component').then(m => m.LoginComponent)
  },
  {
    path: 'register',
    canActivate: [maintenanceGuard],
    loadComponent: () => import('./pages/registration/registration.component').then(m => m.RegistrationComponent)
  },
  {
    path: 'app',
    component: MainLayoutComponent,
    canActivate: [maintenanceGuard, authGuard],
    canActivateChild: [authGuardChild],
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
      },
      {
        path: 'admin-dashboard',
        loadComponent: () => import('./features/admin-dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent)
      },
      {
        path: 'employee-dashboard',
        loadComponent: () => import('./features/employee-dashboard/employee-dashboard.component').then(m => m.EmployeeDashboardComponent)
      },
      {
        path: 'deals-dashboard',
        loadComponent: () => import('./features/deals-dashboard/deals-dashboard.component').then(m => m.DealsDashboardComponent)
      },
      {
        path: 'leads-dashboard',
        loadComponent: () => import('./features/dashboard/leads-dashboard/leads-dashboard.component').then(m => m.LeadsDashboardComponent)
      },
      {
        path: 'companies',
        loadComponent: () => import('./pages/companies/companies.component').then(m => m.CompaniesComponent)
      },
      {
        path: 'companies/:id',
        loadComponent: () => import('./pages/companies/company-detail/company-detail.component').then(m => m.CompanyDetailComponent)
      },
      {
        path: 'employees',
        loadComponent: () => import('./pages/employees/employees.component').then(m => m.EmployeesComponent)
      },
      {
        path: 'employees/:id',
        loadComponent: () => import('./pages/employees/employee-detail/employee-detail.component').then(m => m.EmployeeDetailComponent)
      },
      {
        path: 'employees/:id/edit',
        loadComponent: () => import('./pages/employees/employee-detail/employee-detail.component').then(m => m.EmployeeDetailComponent),
        data: { mode: 'edit' }
      },
      {
        path: 'admin/maintenance',
        loadComponent: () => import('./pages/admin/maintenance-control/maintenance-control.component').then(m => m.MaintenanceControlComponent)
      },
      {
        path: 'profile',
        loadComponent: () => import('./pages/profile/profile.component').then(m => m.ProfileComponent)
      },
      {
        path: 'settings',
        loadComponent: () => import('./pages/settings/settings.component').then(m => m.SettingsComponent)
      },
      {
        path: 'departments',
        loadComponent: () => import('./pages/departments/department-page.component').then(m => m.DepartmentPageComponent)
      }
    ]
  },
  {
    path: 'maintenance',
    loadComponent: () => import('./pages/maintenance/maintenance.component').then(m => m.MaintenanceComponent)
  },
  {
    path: '404',
    loadComponent: () => import('./pages/not-found/not-found.component').then(m => m.NotFoundComponent)
  },
  {
    path: '**',
    redirectTo: '404'
  }
];
